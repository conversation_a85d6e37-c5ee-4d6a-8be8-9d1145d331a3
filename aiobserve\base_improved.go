package aiobserve

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"aes-console/internal/dao"
	"aes-console/internal/lib/types/telemetrylog"

	f "aes-go-module-core/log/formatter"
	"aes-go-module-core/log/logger"
	h "aes-go-module-util/helper"
	"aes-go-module-util/model"
)

const (
	// ServiceTag 服务标签，用于日志记录
	ServiceTag = "AiObserveService"

	// DefaultObserveInterval 默认观测间隔
	DefaultObserveInterval = 3 * time.Second

	// DefaultChannelBuffer 默认通道缓冲区大小
	DefaultChannelBuffer = 100
)

// ObserverService AI观测服务，负责监控和分析AI相关的事件和数据
type ObserverService struct {
	// 基础配置
	ctx    context.Context
	logger logger.Logger

	// 通道和同步
	resultChan chan *ObserverResponse
	mu         sync.RWMutex

	// 业务标识
	incidentID  string
	detectionID string

	// 状态管理
	lastStatus    *ObserverStatus
	currentStatus *ObserverStatus

	// 配置参数
	observeInterval time.Duration
}

// ObserverResponse 观测服务的响应数据结构
type ObserverResponse struct {
	Error     error       `json:"error,omitempty"` // 错误信息
	ID        string      `json:"id"`              // 响应ID
	Event     string      `json:"event"`           // 事件类型
	Data      interface{} `json:"data,omitempty"`  // 响应数据
	Timestamp time.Time   `json:"timestamp"`       // 时间戳
}

// String 返回响应的字符串表示
func (r *ObserverResponse) String() string {
	return fmt.Sprintf("ObserverResponse{ID: %s, Event: %s, Error: %v, HasData: %t, Timestamp: %s}",
		r.ID, r.Event, r.Error, r.Data != nil, r.Timestamp.Format(time.RFC3339))
}

// ObserverStatus 观测状态，包含所有相关的数据和条件
type ObserverStatus struct {
	// 基础数据
	incident   *model.DataIncident
	cgOverview *model.DataCgOverview
	detections []*model.DataDetection

	// 查询条件
	telemetryCondition *TelemetryQueryCondition
	detectionCondition *DetectionQueryCondition
	alertCondition     *AlertQueryCondition
	gptCondition       *GPTQueryCondition

	// 查询结果
	treeIDs          []uint64
	telemetryLogs    []*model.DataCgTelemetryLog
	telemetryRawLogs []*telemetrylog.CgTelemetryLogRawLog
	alertLogs        []*model.DataAlertLog
	gptResults       []*model.DataGptRes
	edgeToSeverity   map[uint64]uint32
}

// 查询条件结构体定义
type TelemetryQueryCondition struct {
	TreeIDs            []uint64
	TelemetryStartTime time.Time
	TelemetryEndTime   time.Time
}

type DetectionQueryCondition struct {
	LastDetectTime time.Time
}

type AlertQueryCondition struct {
	AgentIDs             []string
	TreeIDs              []uint64
	FirstDetectTimeStart time.Time
	FirstDetectTimeEnd   time.Time
	InsertTimeEnd        time.Time
}

type GPTQueryCondition struct {
	AgentIDs        []string
	TreeIDs         []uint64
	DetectTimeStart time.Time
	DetectTimeEnd   time.Time
	InsertTimeEnd   time.Time
}

// ServiceConfig 服务配置
type ServiceConfig struct {
	ObserveInterval time.Duration
	ChannelBuffer   int
}

// NewObserverService 创建AI观测服务实例
func NewObserverService(ctx context.Context, incidentID, detectionID string, config *ServiceConfig) *ObserverService {
	if config == nil {
		config = &ServiceConfig{
			ObserveInterval: DefaultObserveInterval,
			ChannelBuffer:   DefaultChannelBuffer,
		}
	}

	return &ObserverService{
		ctx:             ctx,
		logger:          h.LoggerFromCtx(ctx),
		incidentID:      incidentID,
		detectionID:     detectionID,
		observeInterval: config.ObserveInterval,
		resultChan:      make(chan *ObserverResponse, config.ChannelBuffer),
	}
}

// StartObserving 开始观测，返回结果通道
func (s *ObserverService) StartObserving() (<-chan *ObserverResponse, error) {
	s.logger.Info(ServiceTag, f.WHAT("starting AI observation service"),
		f.KV("incident_id", s.incidentID), f.KV("detection_id", s.detectionID))

	go s.observeLoop()

	return s.resultChan, nil
}

// StopObserving 停止观测
func (s *ObserverService) StopObserving() {
	s.logger.Info(ServiceTag, f.WHAT("stopping AI observation service"))
	close(s.resultChan)
}

// observeLoop 观测循环，定期收集和分析数据
func (s *ObserverService) observeLoop() {
	defer func() {
		if r := recover(); r != nil {
			s.logger.Error(ServiceTag, f.WHAT("observe loop panic"), f.REASON("panic: %v", r))
		}
	}()

	ticker := time.NewTicker(s.observeInterval)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			s.logger.Info(ServiceTag, f.WHAT("observe loop stopped"), f.REASON("context cancelled"))
			return
		case <-ticker.C:
			s.collectAndAnalyzeData()
		}
	}
}

// collectAndAnalyzeData 收集和分析数据的主要逻辑
func (s *ObserverService) collectAndAnalyzeData() {
	s.logger.Debug(ServiceTag, f.WHAT("starting data collection and analysis"))

	// 创建新的状态对象
	status := &ObserverStatus{
		telemetryCondition: &TelemetryQueryCondition{},
		detectionCondition: &DetectionQueryCondition{},
		alertCondition:     &AlertQueryCondition{},
		gptCondition:       &GPTQueryCondition{},
		edgeToSeverity:     make(map[uint64]uint32),
	}

	// 执行数据收集流程
	if err := s.executeDataCollectionPipeline(status); err != nil {
		s.sendErrorResponse("data_collection_failed", err)
		return
	}

	// 构建响应数据
	response, err := s.buildObserverResponse(status)
	if err != nil {
		s.sendErrorResponse("response_build_failed", err)
		return
	}

	// 更新状态并发送响应
	s.updateStatus(status)
	s.sendResponse(response)
}

// executeDataCollectionPipeline 执行数据收集管道
func (s *ObserverService) executeDataCollectionPipeline(status *ObserverStatus) error {
	// 1. 查询基础信息
	if err := s.queryBasicInformation(status); err != nil {
		return fmt.Errorf("query basic information failed: %w", err)
	}

	// 2. 查询树ID列表
	if err := s.queryTreeIDs(status); err != nil {
		return fmt.Errorf("query tree IDs failed: %w", err)
	}

	// 3. 构建查询条件
	s.buildQueryConditions(status)

	// 4. 获取所有相关数据
	if err := s.fetchAllData(status); err != nil {
		return fmt.Errorf("fetch all data failed: %w", err)
	}

	return nil
}

// queryBasicInformation 查询基础信息（事件和CG概览）
func (s *ObserverService) queryBasicInformation(status *ObserverStatus) error {
	// 查询事件详情
	incident, err := dao.QueryIncidentDetail(s.ctx, s.incidentID)
	if err != nil {
		return fmt.Errorf("query incident detail by ID %s: %w", s.incidentID, err)
	}
	status.incident = incident

	// 查询CG概览
	cgOverview, err := dao.GetCgOverviewByCgId(s.ctx, incident.CgID)
	if err != nil {
		return fmt.Errorf("get CG overview by ID %s: %w", incident.CgID, err)
	}
	status.cgOverview = cgOverview

	s.logger.Debug(ServiceTag, f.WHAT("basic information queried successfully"),
		f.KV("incident_id", s.incidentID), f.KV("cg_id", incident.CgID))

	return nil
}

// queryTreeIDs 根据事件状态查询相应的树ID列表
func (s *ObserverService) queryTreeIDs(status *ObserverStatus) error {
	var treeIDs []uint64
	var err error

	incident := status.incident
	cgOverview := status.cgOverview

	switch {
	case s.isIncidentManualClose(incident):
		treeIDs, err = s.handleManualCloseCase(status)
	case s.isIncidentCGMergeClose(incident):
		treeIDs, err = s.handleCGMergeCloseCase(status)
	default:
		treeIDs, err = s.handleNormalCase(status)
	}

	if err != nil {
		return err
	}

	if len(treeIDs) == 0 {
		s.logger.Warn(ServiceTag, f.WHAT("no tree IDs found"), f.REASON("tree ID list is empty"))
		return fmt.Errorf("tree ID list is empty")
	}

	status.treeIDs = treeIDs
	s.logger.Debug(ServiceTag, f.WHAT("tree IDs queried successfully"), f.KV("count", len(treeIDs)))

	return nil
}

// handleManualCloseCase 处理手动关闭事件的情况
func (s *ObserverService) handleManualCloseCase(status *ObserverStatus) ([]uint64, error) {
	incident := status.incident
	cgOverview := status.cgOverview

	var treeIDs []uint64
	var err error

	if s.isCGMergedTo(cgOverview) {
		// 事件手动关闭，CG已被合并
		treeIDs, err = dao.GetTreeIdsByTreeIdsAndTimeRange(s.ctx,
			cgOverview.MergedTreeIDs,
			cgOverview.TelemetryStartTime,
			incident.ClosedTime,
		)
	} else {
		// 事件手动关闭，CG未被合并
		treeIDs, err = dao.GetTreeIdsByTimeRange(s.ctx,
			cgOverview.TelemetryStartTime,
			incident.ClosedTime,
		)
	}

	if err != nil {
		return nil, fmt.Errorf("get tree IDs for manual close case: %w", err)
	}

	// 设置查询条件的时间范围
	status.telemetryCondition.TelemetryStartTime = cgOverview.TelemetryStartTime
	status.telemetryCondition.TelemetryEndTime = incident.ClosedTime
	status.detectionCondition.LastDetectTime = incident.ClosedTime
	status.alertCondition.InsertTimeEnd = incident.ClosedTime
	status.gptCondition.InsertTimeEnd = incident.ClosedTime

	return treeIDs, nil
}

// handleCGMergeCloseCase 处理CG合并关闭事件的情况
func (s *ObserverService) handleCGMergeCloseCase(status *ObserverStatus) ([]uint64, error) {
	incident := status.incident
	cgOverview := status.cgOverview

	// 事件CG合并关闭，直接使用CG概览表记录的树ID
	treeIDs := cgOverview.MergedTreeIDs

	// 设置查询条件的时间范围
	status.telemetryCondition.TelemetryStartTime = cgOverview.TelemetryStartTime
	status.telemetryCondition.TelemetryEndTime = cgOverview.TelemetryEndTime
	status.detectionCondition.LastDetectTime = incident.ClosedTime
	status.alertCondition.InsertTimeEnd = incident.ClosedTime
	status.gptCondition.InsertTimeEnd = incident.ClosedTime

	return treeIDs, nil
}

// handleNormalCase 处理正常事件的情况
func (s *ObserverService) handleNormalCase(status *ObserverStatus) ([]uint64, error) {
	cgOverview := status.cgOverview

	// 事件正常，使用CG概览的时间范围查询
	treeIDs, err := dao.GetTreeIdsByTimeRange(s.ctx,
		cgOverview.TelemetryStartTime,
		cgOverview.TelemetryEndTime,
	)

	if err != nil {
		return nil, fmt.Errorf("get tree IDs for normal case: %w", err)
	}

	return treeIDs, nil
}

// buildQueryConditions 构建各种查询条件
func (s *ObserverService) buildQueryConditions(status *ObserverStatus) {
	incident := status.incident
	detections := status.detections
	treeIDs := status.treeIDs

	// 计算检测时间范围
	firstDetectTime, lastDetectTime := s.calculateDetectionTimeRange(detections)

	// 构建告警查询条件
	status.alertCondition.FirstDetectTimeStart = firstDetectTime
	status.alertCondition.FirstDetectTimeEnd = lastDetectTime
	status.alertCondition.AgentIDs = []string{incident.AgentID}
	status.alertCondition.TreeIDs = treeIDs

	// 构建GPT查询条件
	status.gptCondition.AgentIDs = []string{incident.AgentID}
	status.gptCondition.TreeIDs = treeIDs
	status.gptCondition.DetectTimeStart = incident.FirstDetectTime
	status.gptCondition.DetectTimeEnd = incident.LastDetectTime

	// 构建遥测查询条件
	status.telemetryCondition.TreeIDs = treeIDs

	s.logger.Debug(ServiceTag, f.WHAT("query conditions built successfully"),
		f.KV("tree_ids_count", len(treeIDs)),
		f.KV("agent_id", incident.AgentID))
}

// calculateDetectionTimeRange 计算检测时间范围
func (s *ObserverService) calculateDetectionTimeRange(detections []*model.DataDetection) (time.Time, time.Time) {
	var firstDetectTime, lastDetectTime time.Time

	for _, detection := range detections {
		if firstDetectTime.IsZero() || firstDetectTime.After(detection.FirstDetectTime) {
			firstDetectTime = detection.FirstDetectTime
		}
		if lastDetectTime.Before(detection.LastDetectTime) {
			lastDetectTime = detection.LastDetectTime
		}
	}

	return firstDetectTime, lastDetectTime
}

// fetchAllData 获取所有相关数据
func (s *ObserverService) fetchAllData(status *ObserverStatus) error {
	// 并行获取各种数据以提高性能
	errChan := make(chan error, 4)

	// 获取遥测日志
	go func() {
		errChan <- s.fetchTelemetryData(status)
	}()

	// 获取检测数据
	go func() {
		errChan <- s.fetchDetectionData(status)
	}()

	// 获取告警数据
	go func() {
		errChan <- s.fetchAlertData(status)
	}()

	// 获取GPT结果
	go func() {
		errChan <- s.fetchGPTData(status)
	}()

	// 等待所有数据获取完成
	for i := 0; i < 4; i++ {
		if err := <-errChan; err != nil {
			return err
		}
	}

	return nil
}

// fetchTelemetryData 获取遥测数据
func (s *ObserverService) fetchTelemetryData(status *ObserverStatus) error {
	telemetryLogs, err := dao.GetCgTelemetryLogs(s.ctx, status.telemetryCondition)
	if err != nil {
		return fmt.Errorf("get CG telemetry logs: %w", err)
	}

	// 解析遥测日志原始数据
	telemetryRawLogs := make([]*telemetrylog.CgTelemetryLogRawLog, 0, len(telemetryLogs))
	for _, telemetryLog := range telemetryLogs {
		var rawLogObj telemetrylog.CgTelemetryLogRawLog
		if err := json.Unmarshal([]byte(telemetryLog.RawLog), &rawLogObj); err != nil {
			s.logger.Warn(ServiceTag, f.WHAT("parse telemetry raw log failed"),
				f.REASON("unmarshal error: %v", err))
			continue
		}
		telemetryRawLogs = append(telemetryRawLogs, &rawLogObj)
	}

	status.telemetryLogs = telemetryLogs
	status.telemetryRawLogs = telemetryRawLogs

	s.logger.Debug(ServiceTag, f.WHAT("telemetry data fetched"),
		f.KV("logs_count", len(telemetryLogs)),
		f.KV("raw_logs_count", len(telemetryRawLogs)))

	return nil
}

// fetchDetectionData 获取检测数据
func (s *ObserverService) fetchDetectionData(status *ObserverStatus) error {
	detections, err := dao.QueryDetections(s.ctx, status.detectionCondition)
	if err != nil {
		return fmt.Errorf("query detections: %w", err)
	}

	status.detections = detections

	s.logger.Debug(ServiceTag, f.WHAT("detection data fetched"),
		f.KV("detections_count", len(detections)))

	return nil
}

// fetchAlertData 获取告警数据
func (s *ObserverService) fetchAlertData(status *ObserverStatus) error {
	alertLogs, err := dao.QueryAlertLogs(s.ctx, status.alertCondition)
	if err != nil {
		return fmt.Errorf("query alert logs: %w", err)
	}

	// 提取告警的边ID和严重程度映射
	edgeToSeverity := make(map[uint64]uint32)
	for _, alertLog := range alertLogs {
		for _, edgeID := range alertLog.EdgeIDs {
			// 保留最高严重程度
			if severity, exists := edgeToSeverity[edgeID]; exists {
				if severity < alertLog.Severity {
					edgeToSeverity[edgeID] = alertLog.Severity
				}
			} else {
				edgeToSeverity[edgeID] = alertLog.Severity
			}
		}
	}

	status.alertLogs = alertLogs
	status.edgeToSeverity = edgeToSeverity

	s.logger.Debug(ServiceTag, f.WHAT("alert data fetched"),
		f.KV("alert_logs_count", len(alertLogs)),
		f.KV("edge_severity_mappings", len(edgeToSeverity)))

	return nil
}

// fetchGPTData 获取GPT结果数据
func (s *ObserverService) fetchGPTData(status *ObserverStatus) error {
	gptResults, err := dao.QueryGptRes(s.ctx, status.gptCondition)
	if err != nil {
		return fmt.Errorf("query GPT results: %w", err)
	}

	status.gptResults = gptResults

	s.logger.Debug(ServiceTag, f.WHAT("GPT data fetched"),
		f.KV("gpt_results_count", len(gptResults)))

	return nil
}

// buildObserverResponse 构建观测响应数据
func (s *ObserverService) buildObserverResponse(status *ObserverStatus) (*ObserverResponse, error) {
	// 构建CG响应数据（这里假设有一个buildCGResponse方法）
	cgResponse, err := s.buildCGResponse(status)
	if err != nil {
		return nil, fmt.Errorf("build CG response: %w", err)
	}

	if cgResponse == nil {
		return nil, fmt.Errorf("CG response is nil")
	}

	// 序列化响应数据
	data, err := json.Marshal(cgResponse)
	if err != nil {
		return nil, fmt.Errorf("marshal CG response: %w", err)
	}

	response := &ObserverResponse{
		ID:        fmt.Sprintf("obs_%d", time.Now().UnixNano()),
		Event:     "data_updated",
		Data:      string(data),
		Timestamp: time.Now(),
	}

	return response, nil
}

// buildCGResponse 构建CG响应数据（占位符实现）
func (s *ObserverService) buildCGResponse(status *ObserverStatus) (interface{}, error) {
	// 这里应该根据实际业务逻辑构建CG响应
	// 目前返回一个包含基本信息的结构
	response := map[string]interface{}{
		"incident_id":          s.incidentID,
		"detection_id":         s.detectionID,
		"tree_ids_count":       len(status.treeIDs),
		"telemetry_logs_count": len(status.telemetryLogs),
		"alert_logs_count":     len(status.alertLogs),
		"gpt_results_count":    len(status.gptResults),
		"edge_severity_count":  len(status.edgeToSeverity),
		"timestamp":            time.Now(),
	}

	return response, nil
}

// updateStatus 更新服务状态
func (s *ObserverService) updateStatus(newStatus *ObserverStatus) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.lastStatus = s.currentStatus
	s.currentStatus = newStatus
}

// sendResponse 发送响应到通道
func (s *ObserverService) sendResponse(response *ObserverResponse) {
	select {
	case s.resultChan <- response:
		s.logger.Debug(ServiceTag, f.WHAT("response sent successfully"), f.KV("response_id", response.ID))
	case <-s.ctx.Done():
		s.logger.Info(ServiceTag, f.WHAT("context cancelled while sending response"))
	default:
		s.logger.Warn(ServiceTag, f.WHAT("response channel full, dropping response"), f.KV("response_id", response.ID))
	}
}

// sendErrorResponse 发送错误响应
func (s *ObserverService) sendErrorResponse(event string, err error) {
	response := &ObserverResponse{
		ID:        fmt.Sprintf("err_%d", time.Now().UnixNano()),
		Event:     event,
		Error:     err,
		Timestamp: time.Now(),
	}

	s.logger.Error(ServiceTag, f.WHAT("sending error response"),
		f.KV("event", event), f.REASON("error: %v", err))

	s.sendResponse(response)
}

// 状态判断方法
// isCGMergedTo 判断CG是否已被合并
func (s *ObserverService) isCGMergedTo(cgOverview *model.DataCgOverview) bool {
	return cgOverview.Merged != 0
}

// isIncidentManualClose 判断事件是否手动关闭
func (s *ObserverService) isIncidentManualClose(incident *model.DataIncident) bool {
	return incident.Closed == 2
}

// isIncidentCGMergeClose 判断事件是否因CG合并而关闭
func (s *ObserverService) isIncidentCGMergeClose(incident *model.DataIncident) bool {
	return incident.Closed == 1
}

// GetCurrentStatus 获取当前状态（只读）
func (s *ObserverService) GetCurrentStatus() *ObserverStatus {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.currentStatus
}

// GetLastStatus 获取上一次状态（只读）
func (s *ObserverService) GetLastStatus() *ObserverStatus {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.lastStatus
}
