package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"aiobserve"
)

// 使用示例：展示如何使用改进后的AI观测服务
func main() {
	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// 配置服务参数
	config := &aiobserve.ServiceConfig{
		ObserveInterval: 5 * time.Second, // 每5秒观测一次
		ChannelBuffer:   50,              // 通道缓冲区大小
	}

	// 创建观测服务实例
	service := aiobserve.NewObserverService(ctx, "incident_123", "detection_456", config)

	// 开始观测
	resultChan, err := service.StartObserving()
	if err != nil {
		log.Fatalf("Failed to start observing: %v", err)
	}

	// 处理观测结果
	go func() {
		for {
			select {
			case response, ok := <-resultChan:
				if !ok {
					log.Println("Result channel closed")
					return
				}
				
				if response.Error != nil {
					log.Printf("Received error response: %v", response.Error)
				} else {
					log.Printf("Received response: %s", response.String())
					// 处理具体的数据
					handleObserverResponse(response)
				}
				
			case <-ctx.Done():
				log.Println("Context cancelled, stopping result processing")
				return
			}
		}
	}()

	// 模拟运行一段时间
	time.Sleep(30 * time.Second)

	// 停止观测
	service.StopObserving()
	log.Println("Observer service stopped")
}

// handleObserverResponse 处理观测响应
func handleObserverResponse(response *aiobserve.ObserverResponse) {
	switch response.Event {
	case "data_updated":
		fmt.Printf("Data updated at %s\n", response.Timestamp.Format(time.RFC3339))
		if response.Data != nil {
			fmt.Printf("Response data: %v\n", response.Data)
		}
		
	case "data_collection_failed":
		fmt.Printf("Data collection failed: %v\n", response.Error)
		
	case "response_build_failed":
		fmt.Printf("Response build failed: %v\n", response.Error)
		
	default:
		fmt.Printf("Unknown event type: %s\n", response.Event)
	}
}

// 高级使用示例：展示如何监控服务状态
func advancedUsageExample() {
	ctx := context.Background()
	
	// 使用默认配置创建服务
	service := aiobserve.NewObserverService(ctx, "incident_789", "detection_101", nil)
	
	// 开始观测
	resultChan, _ := service.StartObserving()
	
	// 定期检查服务状态
	statusTicker := time.NewTicker(10 * time.Second)
	defer statusTicker.Stop()
	
	go func() {
		for {
			select {
			case <-statusTicker.C:
				currentStatus := service.GetCurrentStatus()
				lastStatus := service.GetLastStatus()
				
				if currentStatus != nil {
					fmt.Printf("Current status - Tree IDs: %d, Telemetry Logs: %d\n",
						len(currentStatus.TreeIDs), len(currentStatus.TelemetryLogs))
				}
				
				if lastStatus != nil {
					fmt.Printf("Last status - Tree IDs: %d, Telemetry Logs: %d\n",
						len(lastStatus.TreeIDs), len(lastStatus.TelemetryLogs))
				}
				
			case <-ctx.Done():
				return
			}
		}
	}()
	
	// 处理结果
	for response := range resultChan {
		fmt.Printf("Advanced handler received: %s\n", response.String())
	}
}
