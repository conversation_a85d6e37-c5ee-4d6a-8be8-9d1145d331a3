package aiobserve

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"aes-console/internal/dao"
	"aes-console/internal/lib/types/telemetrylog"

	f "aes-go-module-core/log/formatter"
	"aes-go-module-core/log/logger"
	h "aes-go-module-util/helper"
	"aes-go-module-util/model"
)

const tag = "AiObserveService"

// Service AI观测仓服务
type Service struct {
	ctx    context.Context
	logger logger.Logger
	ch     chan *AiObsererRes

	incidentID  string
	detectionID string

	lastStatus *aiObserverStatus
	currStatus *aiObserverStatus
}

// AiObsererRes 响应数据
type AiObsererRes struct {
	Err   error  `json:"error"`
	ID    string `json:"id"`
	Event string `json:"event"`
	Data  any    `json:"data"`
}

func (r *AiObsererRes) String() string {
	return fmt.Sprintf("AiObsererRes{ID: %s, Event: %s, Error: %v, HasData: %t}",
		r.ID, r.Event, r.Err, r.Data != nil)
}

// New 创建AI观测仓服务
func New(ctx context.Context, incidentID, detectionID string) *Service {
	return &Service{
		ctx:         ctx,
		logger:      h.LoggerFromCtx(ctx),
		incidentID:  incidentID,
		detectionID: detectionID,
	}
}

// Observe 观测
func (s *Service) Observe() (chan *AiObsererRes, error) {
	s.ch = make(chan *AiObsererRes)

	go s.loop()

	return s.ch, nil
}

func (s *Service) loop() {
	// mock
	// FIXME 从配置文件获取
	ticker := time.NewTicker(time.Second * 3)
	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			// res := &AiObsererRes{
			// 	ID:    fmt.Sprintf("%v", id),
			// 	Event: fmt.Sprintf("event-%d", id),
			// 	Data:  fmt.Sprintf("data-%d", id),
			// }

			// s.ch <- res
			s.getData()
			// time.Sleep(time.Second)
		}
	}
}

func (s *Service) getData() {
	logger := h.LoggerFromCtx(s.ctx)
	s.queryBasicInfo()
	s.queryTreeIds()
	s.buildQueryCond()
	err := s.fetchStatus()
	if err != nil {
		// FIXME LOG
		logger.Warn(tag, f.WHAT("get data failed"), f.REASON("fetch status error: %v", err))
	}
	cgResponse, _ := s.buildCg()
	if cgResponse == nil {
		return
	}

	data, _ := json.Marshal(cgResponse)
	res := &AiObsererRes{
		ID:    "111",
		Event: "event-1",
		Data:  string(data),
	}

	s.lastStatus = s.currStatus
	s.ch <- res
}

func (s *Service) buildAiWorkflow() {

}

func (s *Service) buildQueryCond() {

}

func (s *Service) queryBasicInfo() error {
	// 1. 查询事件信息
	incident, err := dao.QueryIncidentDetail(s.ctx, s.incidentID)
	if err != nil {
		return fmt.Errorf("query incident by id %v error: %v", s.incidentID, err)
	}

	status := &aiObserverStatus{}
	status.incident = incident

	// 2. 查询CG overview表
	cgOverView, err := dao.GetCgOverviewByCgId(s.ctx, incident.CgID)
	if err != nil {
		return fmt.Errorf("get cg overview by id %v error: %v", incident.CgID, err)
	}
	status.cgOverview = cgOverView
	s.currStatus = status

	return nil
}

func (s *Service) queryTreeIds() error {
	status := s.currStatus

	// 查询treeid
	var err error
	var treeIds []uint64
	if s.isIncidentManualClose(status.incident) {
		if s.isCgMergeTo(status.cgOverview) {
			// 事件手动关闭，cg已被合并
			treeIds, err = dao.GetTreeIdsByTreeIdsAndTimeRange(s.ctx,
				status.cgOverview.MergedTreeIDs,
				status.cgOverview.TelemetryStartTime,
				status.incident.ClosedTime,
			)
			if err != nil {
				return fmt.Errorf("get treeid list by time range and treeids error: %v", err)
			}
		} else {
			treeIds, err = dao.GetTreeIdsByTimeRange(s.ctx,
				status.cgOverview.TelemetryStartTime,
				status.incident.ClosedTime,
			)
			if err != nil {
				return fmt.Errorf("get treeid list by time range error: %v", err)
			}
		}

		status.telemetryCond.TelemetryStartTime = status.cgOverview.TelemetryStartTime
		status.telemetryCond.TelemetryEndTime = status.incident.ClosedTime
		status.detectionCond.LastDetectTime = status.incident.ClosedTime
		status.alertCond.InsertTimeEnd = status.incident.ClosedTime
		status.gptCond.InsertTimeEnd = status.incident.ClosedTime
	} else if s.isIncidentCgmergeClose(status.incident) {
		// 事件cg合并关闭，直接使用cg_overview表记录的treeid
		treeIds = status.cgOverview.MergedTreeIDs

		status.telemetryCond.TelemetryStartTime = status.cgOverview.TelemetryStartTime
		status.telemetryCond.TelemetryEndTime = status.cgOverview.TelemetryEndTime
		status.detectionCond.LastDetectTime = status.incident.ClosedTime
		status.alertCond.InsertTimeEnd = status.incident.ClosedTime
		status.gptCond.InsertTimeEnd = status.incident.ClosedTime
	} else {
		// 事件正常
		treeIds, err = dao.GetTreeIdsByTimeRange(s.ctx,
			status.cgOverview.TelemetryStartTime,
			status.cgOverview.TelemetryEndTime,
		)
		if err != nil {
			return fmt.Errorf("get treeid list by time range error: %v", err)
		}
	}

	if len(treeIds) == 0 {
		// FIXME 不一定是错误，可能只是没有新数据
		return fmt.Errorf("treeid list is empty")
	}

	var firstDetectTime time.Time
	for _, detect := range status.detections {
		if firstDetectTime.IsZero() || firstDetectTime.After(detect.FirstDetectTime) {
			firstDetectTime = detect.FirstDetectTime
		}
	}

	var lastDetectTime time.Time
	for _, detect := range status.detections {
		if lastDetectTime.Before(detect.LastDetectTime) {
			lastDetectTime = detect.LastDetectTime
		}
	}

	s.logger.Debug(tag, "query tree ids is %v", treeIds)
	status.alertCond.FirstDetectTimeStart = firstDetectTime
	status.alertCond.FirstDetectTimeEnd = lastDetectTime
	status.alertCond.AgentIDs = []string{status.incident.AgentID}
	status.alertCond.TreeIDs = treeIds
	status.treeIds = treeIds

	status.gptCond.AgentIDs = []string{status.incident.AgentID}
	status.gptCond.TreeIDs = treeIds
	status.gptCond.DetectTimeStart = status.incident.FirstDetectTime
	status.gptCond.DetectTimeEnd = status.incident.LastDetectTime

	status.telemetryCond.TreeIds = treeIds

	return nil
}

func (s *Service) fetchStatus() error {
	status := s.currStatus
	// 查询遥测
	telemetryLogs, err := dao.GetCgTelemetryLogs(s.ctx, &status.telemetryCond)
	if err != nil {
		return fmt.Errorf("get cg telemetry logs error: %v", err)
	}

	telemetryRawlogs := make([]*telemetrylog.CgTelemetryLogRawLog, 0, len(telemetryLogs))

	// 解析遥测日志rawLog
	for _, telemetryLog := range telemetryLogs {
		var rawlogObj telemetrylog.CgTelemetryLogRawLog
		err = json.Unmarshal([]byte(telemetryLog.RawLog), &rawlogObj)
		if err != nil {
			s.logger.Info(tag, "parse telemetry rawlog to json error: %v, will ignore this log", err)
			continue
		}

		telemetryRawlogs = append(telemetryRawlogs, &rawlogObj)
	}

	// 查询detection
	detections, err := dao.QueryDetections(s.ctx, &status.detectionCond)
	if err != nil {
		return fmt.Errorf("get detections error: %v", err)
	}

	// 查询alert
	alertLogs, err := dao.QueryAlertLogs(s.ctx, &status.alertCond)
	if err != nil {
		return fmt.Errorf("get aletr logs error: %v", err)
	}

	// 提取alert的边id
	edgeToSeverity := make(map[uint64]uint32)
	for _, alertLog := range alertLogs {
		for _, edgeID := range alertLog.EdgeIDs {
			// FIXME 最高等级?
			if severity, ok := edgeToSeverity[edgeID]; ok {
				if severity < alertLog.Severity {
					edgeToSeverity[edgeID] = alertLog.Severity
				}
			} else {
				edgeToSeverity[edgeID] = alertLog.Severity
			}
		}
	}
	s.logger.Debug(tag, "xxx edge -> severity %+v", edgeToSeverity)

	// 查询AI工作流
	gptRes, err := dao.QueryGptRes(s.ctx, &status.gptCond)
	if err != nil {
		return fmt.Errorf("get gpt res error: %v", err)
	}

	status.edgeToSeverity = edgeToSeverity
	status.telemetryLogs = telemetryLogs
	status.telemetryRawLogs = telemetryRawlogs
	status.alertLogs = alertLogs
	status.gptRes = gptRes
	status.detections = detections

	s.currStatus = status
	return nil
}

func (s *Service) isCgClosed(cgOverview *model.DataCgOverview) bool {
	// TODO cg关闭判断 fxs
	return false
}

func (s *Service) isCgMergeTo(cgOverView *model.DataCgOverview) bool {
	return cgOverView.Merged != 0
}

func (s *Service) isIncidentManualClose(incident *model.DataIncident) bool {
	return incident.Closed == 2
}

func (s *Service) isIncidentCgmergeClose(incident *model.DataIncident) bool {
	return incident.Closed == 1
}
